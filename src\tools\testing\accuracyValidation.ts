/**
 * Accuracy Validation Framework
 *
 * This module provides comprehensive validation to ensure that our mod analysis
 * results are accurate and reliable. It compares extracted data against known
 * reference data and validates the integrity of our analysis system.
 */

import { DatabaseService } from '../../services/databaseService.js';
import { PackageAnalyzer } from '../../services/analysis/packageAnalyzer.js';
import { createPackageAnalyzer } from '../../services/analysis/packageAnalyzerFactory.js';
import { Logger } from '../../utils/logging/logger.js';
import { ResourceTracker, ResourceType, ResourceState } from '../../utils/memory/resourceTracker.js';
import { AccuracyMeasurementService } from '../../services/validation/AccuracyMeasurementService.js';
import { ConflictDetectionService } from '../../services/conflict/conflictDetectionService.js';
import * as path from 'path';
import * as fs from 'fs';
import { Package } from '@s4tk/models';

/**
 * Reference data for validation
 */
export interface ReferenceModData {
    fileName: string;
    expectedResourceCount: number;
    expectedResourceTypes: string[];
    expectedFileSize: number;
    knownResources: {
        type: string;
        group: string;
        instance: string;
        expectedSize?: number;
        expectedMetadata?: any;
    }[];
    description: string;
}

/**
 * Validation result interface
 */
export interface ValidationResult {
    success: boolean;
    testName: string;
    duration: number;
    details: {
        resourceCountMatch: boolean;
        resourceTypesMatch: boolean;
        metadataAccuracy: number;
        performanceMetrics: any;
        memoryUsage: any;
        conflictDetectionAccuracy?: {
            overall: number;
            precision: number;
            recall: number;
            f1Score: number;
            falsePositiveRate: number;
            truePositives: number;
            falsePositives: number;
            falseNegatives: number;
            trueNegatives: number;
            recommendations: string[];
        };
    };
    errors: string[];
    warnings: string[];
    accuracy: {
        resourceCount: { expected: number; actual: number; match: boolean };
        resourceTypes: { expected: string[]; actual: string[]; match: boolean };
        metadata: { validated: number; correct: number; accuracy: number };
        conflictDetection?: {
            realAccuracy: number;
            replacesHardcoded: boolean;
            isAcceptable: boolean;
            detectorAccuracy: { [detectorType: string]: number };
        };
    };
}

/**
 * Create reference data for known mods
 */
export function createReferenceData(): ReferenceModData[] {
    return [
        {
            fileName: 'simple_test_mod.package',
            expectedResourceCount: 1,
            expectedResourceTypes: ['STRING_TABLE'],
            expectedFileSize: 1024, // Approximate
            knownResources: [
                {
                    type: '0x220557DA',
                    group: '0x80000000',
                    instance: '0x12345678',
                    expectedSize: 500,
                    expectedMetadata: {
                        hasStrings: true,
                        language: 'English'
                    }
                }
            ],
            description: 'Simple string table mod for basic validation'
        }
        // Add more reference mods as needed
    ];
}

/**
 * Validate resource count accuracy
 */
export async function validateResourceCount(
    packagePath: string,
    expectedCount: number,
    databaseService: DatabaseService
): Promise<{ match: boolean; expected: number; actual: number; details: string }> {
    const logger = new Logger('ResourceCountValidator');

    try {
        // Method 1: Direct S4TK package reading
        const packageBuffer = fs.readFileSync(packagePath);
        const s4tkPackage = Package.from(packageBuffer);
        const s4tkCount = s4tkPackage.size;

        // Method 2: Our analysis system
        const packageAnalyzer = createPackageAnalyzer(databaseService, 'streaming');
        await packageAnalyzer.initialize();

        const analysisResult = await packageAnalyzer.analyzePackage(packagePath);

        // Method 3: Database query
        const dbResources = await databaseService.executeQuery(
            'SELECT COUNT(*) as count FROM Resources WHERE packagePath = ?',
            [packagePath]
        );
        const dbCount = dbResources[0]?.count || 0;

        const match = s4tkCount === expectedCount;
        const details = `S4TK: ${s4tkCount}, Expected: ${expectedCount}, DB: ${dbCount}, Analysis Success: ${analysisResult.success}`;

        logger.info(`Resource count validation: ${details}`);

        return {
            match,
            expected: expectedCount,
            actual: s4tkCount,
            details
        };

    } catch (error: any) {
        logger.error(`Error validating resource count: ${error.message}`);
        return {
            match: false,
            expected: expectedCount,
            actual: -1,
            details: `Error: ${error.message}`
        };
    }
}

/**
 * Validate extracted metadata accuracy
 */
export async function validateMetadataAccuracy(
    packagePath: string,
    referenceData: ReferenceModData,
    databaseService: DatabaseService
): Promise<{ validated: number; correct: number; accuracy: number; details: any[] }> {
    const logger = new Logger('MetadataValidator');
    const details: any[] = [];
    let validated = 0;
    let correct = 0;

    try {
        for (const knownResource of referenceData.knownResources) {
            validated++;

            // Query our database for this resource
            const dbResource = await databaseService.executeQuery(`
                SELECT * FROM Resources
                WHERE resourceType = ? AND groupId = ? AND instanceId = ?
            `, [knownResource.type, knownResource.group, knownResource.instance]);

            if (dbResource.length === 0) {
                details.push({
                    resource: knownResource,
                    status: 'NOT_FOUND',
                    message: 'Resource not found in database'
                });
                continue;
            }

            const resource = dbResource[0];
            let resourceCorrect = true;
            const checks: any[] = [];

            // Validate size if expected
            if (knownResource.expectedSize) {
                const sizeMatch = Math.abs(resource.size - knownResource.expectedSize) < 100; // Allow 100 byte tolerance
                checks.push({
                    property: 'size',
                    expected: knownResource.expectedSize,
                    actual: resource.size,
                    match: sizeMatch
                });
                if (!sizeMatch) resourceCorrect = false;
            }

            // Validate metadata if expected
            if (knownResource.expectedMetadata) {
                const metadata = JSON.parse(resource.metadata || '{}');
                for (const [key, expectedValue] of Object.entries(knownResource.expectedMetadata)) {
                    const actualValue = metadata[key];
                    const metadataMatch = actualValue === expectedValue;
                    checks.push({
                        property: `metadata.${key}`,
                        expected: expectedValue,
                        actual: actualValue,
                        match: metadataMatch
                    });
                    if (!metadataMatch) resourceCorrect = false;
                }
            }

            if (resourceCorrect) correct++;

            details.push({
                resource: knownResource,
                status: resourceCorrect ? 'CORRECT' : 'INCORRECT',
                checks
            });
        }

        const accuracy = validated > 0 ? (correct / validated) * 100 : 0;

        logger.info(`Metadata validation: ${correct}/${validated} correct (${accuracy.toFixed(1)}%)`);

        return { validated, correct, accuracy, details };

    } catch (error: any) {
        logger.error(`Error validating metadata: ${error.message}`);
        return { validated: 0, correct: 0, accuracy: 0, details: [{ error: error.message }] };
    }
}

/**
 * Validate conflict detection accuracy using ground truth scenarios
 * This replaces hardcoded 95% accuracy values with real measurements
 */
export async function validateConflictDetectionAccuracy(
    databaseService: DatabaseService,
    logger: Logger
): Promise<{
    overall: number;
    precision: number;
    recall: number;
    f1Score: number;
    falsePositiveRate: number;
    truePositives: number;
    falsePositives: number;
    falseNegatives: number;
    trueNegatives: number;
    recommendations: string[];
    detectorAccuracy: { [detectorType: string]: number };
    replacesHardcoded: boolean;
    isAcceptable: boolean;
}> {
    try {
        logger.info('🎯 Validating conflict detection accuracy against ground truth scenarios...');

        // Initialize accuracy measurement service
        const conflictDetectionService = ConflictDetectionService.getInstance();
        const accuracyService = new AccuracyMeasurementService(
            databaseService,
            conflictDetectionService,
            {
                enableRealTimeValidation: false,
                logDetailedResults: true,
                confidenceThreshold: 0.7
            },
            logger
        );

        await accuracyService.initialize();

        // Measure overall accuracy
        const metrics = await accuracyService.measureAccuracy();

        // Get improvement recommendations
        const recommendations = accuracyService.getValidationRecommendations();

        // Test individual detectors
        const detectorAccuracy: { [detectorType: string]: number } = {};

        try {
            const lshAccuracy = await accuracyService.measureDetectorAccuracy('lsh');
            detectorAccuracy['LSH'] = lshAccuracy.accuracy * 100;
        } catch (error: any) {
            logger.warn(`LSH detector accuracy measurement failed: ${error.message}`);
            detectorAccuracy['LSH'] = 0;
        }

        try {
            const tgiAccuracy = await accuracyService.measureDetectorAccuracy('tgi');
            detectorAccuracy['TGI'] = tgiAccuracy.accuracy * 100;
        } catch (error: any) {
            logger.warn(`TGI detector accuracy measurement failed: ${error.message}`);
            detectorAccuracy['TGI'] = 0;
        }

        const overallAccuracy = metrics.accuracy * 100;
        const isAcceptable = accuracyService.isAccuracyAcceptable();

        logger.info(`✅ Conflict detection accuracy validation complete:`);
        logger.info(`   - Overall Accuracy: ${overallAccuracy.toFixed(1)}% (replaces hardcoded 95%)`);
        logger.info(`   - Precision: ${(metrics.precision * 100).toFixed(1)}%`);
        logger.info(`   - Recall: ${(metrics.recall * 100).toFixed(1)}%`);
        logger.info(`   - F1 Score: ${(metrics.f1Score * 100).toFixed(1)}%`);
        logger.info(`   - Acceptable: ${isAcceptable ? 'YES' : 'NO'}`);

        return {
            overall: overallAccuracy,
            precision: metrics.precision * 100,
            recall: metrics.recall * 100,
            f1Score: metrics.f1Score * 100,
            falsePositiveRate: metrics.falsePositiveRate * 100,
            truePositives: metrics.truePositives,
            falsePositives: metrics.falsePositives,
            falseNegatives: metrics.falseNegatives,
            trueNegatives: metrics.trueNegatives,
            recommendations,
            detectorAccuracy,
            replacesHardcoded: true,
            isAcceptable
        };

    } catch (error: any) {
        logger.error(`❌ Error validating conflict detection accuracy: ${error.message}`);

        return {
            overall: 0,
            precision: 0,
            recall: 0,
            f1Score: 0,
            falsePositiveRate: 100,
            truePositives: 0,
            falsePositives: 0,
            falseNegatives: 0,
            trueNegatives: 0,
            recommendations: [`Error during validation: ${error.message}`],
            detectorAccuracy: {},
            replacesHardcoded: false,
            isAcceptable: false
        };
    }
}

/**
 * Validate memory usage and performance
 */
export function validatePerformanceMetrics(
    startTime: number,
    startMemory: NodeJS.MemoryUsage,
    endMemory: NodeJS.MemoryUsage,
    resourceCount: number
): any {
    const duration = Date.now() - startTime;
    const memoryDelta = {
        heapUsed: endMemory.heapUsed - startMemory.heapUsed,
        heapTotal: endMemory.heapTotal - startMemory.heapTotal,
        external: endMemory.external - startMemory.external,
        rss: endMemory.rss - startMemory.rss
    };

    const performanceMetrics = {
        duration,
        resourcesPerSecond: resourceCount > 0 ? (resourceCount / (duration / 1000)) : 0,
        memoryPerResource: resourceCount > 0 ? (memoryDelta.heapUsed / resourceCount) : 0,
        memoryEfficiency: memoryDelta.heapUsed < (50 * 1024 * 1024), // Less than 50MB increase
        timeEfficiency: duration < (resourceCount * 1000) // Less than 1 second per resource
    };

    return { memoryDelta, performanceMetrics };
}

/**
 * Run comprehensive accuracy validation
 */
export async function runAccuracyValidation(
    modsPath: string,
    options: { maxMods?: number; useReferenceData?: boolean } = {}
): Promise<ValidationResult> {
    const startTime = Date.now();
    const startMemory = process.memoryUsage();
    const logger = new Logger('AccuracyValidation');
    const errors: string[] = [];
    const warnings: string[] = [];

    logger.info('===== ACCURACY VALIDATION TEST =====');

    try {
        // Initialize database
        const databaseService = new DatabaseService(':memory:');
        await databaseService.initialize();

        const testId = Date.now();
        const resourceTracker = ResourceTracker.getInstance();
        resourceTracker.trackResource(
            ResourceType.DATABASE,
            `accuracyTest_${testId}`,
            async () => await databaseService.close(),
            { id: `db_accuracy_${testId}`, state: ResourceState.ACTIVE }
        );

        // Get reference data
        const referenceData = createReferenceData();
        let resourceCountMatch = false;
        let resourceTypesMatch = false;
        let metadataAccuracy = 0;

        // For now, test with a simple validation approach
        // TODO: Implement full reference data testing when we have known good mods

        // Test 1: Basic resource counting with a real mod
        const packageFiles = fs.readdirSync(modsPath)
            .filter(file => file.endsWith('.package'))
            .slice(0, options.maxMods || 1)
            .map(file => path.join(modsPath, file));

        if (packageFiles.length > 0) {
            const testPackage = packageFiles[0];
            logger.info(`Testing accuracy with: ${path.basename(testPackage)}`);

            // Direct S4TK validation
            const packageBuffer = fs.readFileSync(testPackage);
            const s4tkPackage = Package.from(packageBuffer);
            const expectedCount = s4tkPackage.size;
            const expectedTypes = Array.from(new Set(
                Array.from(s4tkPackage.entries()).map(entry => entry.key.type.toString(16).toUpperCase())
            ));

            // Our analysis
            const packageAnalyzer = createPackageAnalyzer(databaseService, 'streaming');
            await packageAnalyzer.initialize();

            const analysisResult = await packageAnalyzer.analyzePackage(testPackage);

            if (analysisResult.success) {
                // Check database for actual results
                const dbResources = await databaseService.executeQuery(
                    'SELECT COUNT(*) as count FROM Resources WHERE packagePath = ?',
                    [testPackage]
                );
                const actualCount = dbResources[0]?.count || 0;

                const dbTypes = await databaseService.executeQuery(
                    'SELECT DISTINCT resourceType FROM Resources WHERE packagePath = ?',
                    [testPackage]
                );
                const actualTypes = dbTypes.map((row: any) => row.resourceType);

                resourceCountMatch = actualCount === expectedCount;
                resourceTypesMatch = expectedTypes.length === actualTypes.length;

                if (!resourceCountMatch) {
                    errors.push(`Resource count mismatch: expected ${expectedCount}, got ${actualCount}`);
                }

                if (!resourceTypesMatch) {
                    warnings.push(`Resource type count mismatch: expected ${expectedTypes.length}, got ${actualTypes.length}`);
                }

                logger.info(`Validation results: Count match: ${resourceCountMatch}, Type match: ${resourceTypesMatch}`);

            } else {
                errors.push(`Package analysis failed: ${analysisResult.error}`);
            }
        } else {
            warnings.push('No package files found for validation');
        }

        // NEW: Conflict Detection Accuracy Validation
        logger.info('🎯 Running conflict detection accuracy validation...');
        let conflictDetectionAccuracy;
        let conflictDetectionData;

        try {
            conflictDetectionAccuracy = await validateConflictDetectionAccuracy(databaseService, logger);
            conflictDetectionData = {
                realAccuracy: conflictDetectionAccuracy.overall,
                replacesHardcoded: conflictDetectionAccuracy.replacesHardcoded,
                isAcceptable: conflictDetectionAccuracy.isAcceptable,
                detectorAccuracy: conflictDetectionAccuracy.detectorAccuracy
            };

            logger.info(`✅ Conflict detection accuracy: ${conflictDetectionAccuracy.overall.toFixed(1)}% (replaces hardcoded 95%)`);

            if (!conflictDetectionAccuracy.isAcceptable) {
                warnings.push(`Conflict detection accuracy (${conflictDetectionAccuracy.overall.toFixed(1)}%) is below acceptable threshold`);
            }

        } catch (error: any) {
            errors.push(`Conflict detection accuracy validation failed: ${error.message}`);
            conflictDetectionAccuracy = null;
            conflictDetectionData = {
                realAccuracy: 0,
                replacesHardcoded: false,
                isAcceptable: false,
                detectorAccuracy: {}
            };
        }

        // Performance validation
        const endMemory = process.memoryUsage();
        const performanceData = validatePerformanceMetrics(startTime, startMemory, endMemory, 1);

        // Cleanup
        await resourceTracker.releaseResourcesByOwner(`accuracyTest_${testId}`);

        return {
            success: errors.length === 0,
            testName: 'Comprehensive Accuracy Validation Test',
            duration: Date.now() - startTime,
            details: {
                resourceCountMatch,
                resourceTypesMatch,
                metadataAccuracy,
                performanceMetrics: performanceData.performanceMetrics,
                memoryUsage: performanceData.memoryDelta,
                conflictDetectionAccuracy
            },
            errors,
            warnings,
            accuracy: {
                resourceCount: { expected: 0, actual: 0, match: resourceCountMatch },
                resourceTypes: { expected: [], actual: [], match: resourceTypesMatch },
                metadata: { validated: 0, correct: 0, accuracy: metadataAccuracy },
                conflictDetection: conflictDetectionData
            }
        };

    } catch (error: any) {
        errors.push(`Critical validation error: ${error.message}`);
        return {
            success: false,
            testName: 'Comprehensive Accuracy Validation Test',
            duration: Date.now() - startTime,
            details: {
                resourceCountMatch: false,
                resourceTypesMatch: false,
                metadataAccuracy: 0,
                performanceMetrics: {},
                memoryUsage: {},
                conflictDetectionAccuracy: null
            },
            errors,
            warnings,
            accuracy: {
                resourceCount: { expected: 0, actual: 0, match: false },
                resourceTypes: { expected: [], actual: [], match: false },
                metadata: { validated: 0, correct: 0, accuracy: 0 },
                conflictDetection: {
                    realAccuracy: 0,
                    replacesHardcoded: false,
                    isAcceptable: false,
                    detectorAccuracy: {}
                }
            }
        };
    }
}
