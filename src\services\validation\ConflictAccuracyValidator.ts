import { Logger } from '../../utils/logging/logger.js';
import { DatabaseService } from '../databaseService.js';
import { ConflictInfo, ConflictType, ConflictSeverity } from '../../types/conflict/index.js';
import { ResourceInfo } from '../../types/database.js';

/**
 * Ground truth conflict scenario for validation
 */
export interface GroundTruthConflict {
    id: string;
    name: string;
    description: string;
    resource1: ResourceInfo;
    resource2: ResourceInfo;
    expectedConflict: boolean;
    expectedType?: ConflictType;
    expectedSeverity?: ConflictSeverity;
    gameplayImpact: 'NONE' | 'VISUAL' | 'FUNCTIONAL' | 'BREAKING';
    source: 'KNOWN_SCENARIO' | 'COMMUNITY_REPORTED' | 'EXPERT_VALIDATED';
    confidence: number; // How confident we are in this ground truth (0-1)
}

/**
 * Validation result for a single conflict scenario
 */
export interface ValidationResult {
    groundTruthId: string;
    detected: boolean;
    correctType: boolean;
    correctSeverity: boolean;
    detectedConflicts: ConflictInfo[];
    truePositive: boolean;
    falsePositive: boolean;
    falseNegative: boolean;
    confidence: number;
    notes: string[];
}

/**
 * Overall accuracy metrics
 */
export interface AccuracyMetrics {
    totalScenarios: number;
    truePositives: number;
    falsePositives: number;
    falseNegatives: number;
    trueNegatives: number;
    precision: number; // TP / (TP + FP)
    recall: number;    // TP / (TP + FN)
    f1Score: number;   // 2 * (precision * recall) / (precision + recall)
    accuracy: number;  // (TP + TN) / (TP + TN + FP + FN)
    typeAccuracy: number; // Percentage of correctly identified conflict types
    severityAccuracy: number; // Percentage of correctly identified severities
    averageConfidence: number;
    detectionRate: number; // Percentage of conflicts detected
    falsePositiveRate: number; // FP / (FP + TN)
}

/**
 * Conflict Detection Accuracy Validator
 * Validates the accuracy of conflict detection algorithms against known ground truth scenarios
 */
export class ConflictAccuracyValidator {
    private logger: Logger;
    private databaseService: DatabaseService;
    private groundTruthScenarios: GroundTruthConflict[] = [];

    constructor(databaseService: DatabaseService, logger?: Logger) {
        this.databaseService = databaseService;
        this.logger = logger || new Logger('ConflictAccuracyValidator');
    }

    /**
     * Initialize the validator with ground truth scenarios
     */
    public async initialize(): Promise<void> {
        this.logger.info('Initializing ConflictAccuracyValidator...');

        // Load ground truth scenarios
        await this.loadGroundTruthScenarios();

        this.logger.info(`Loaded ${this.groundTruthScenarios.length} ground truth scenarios`);
    }

    /**
     * Load ground truth conflict scenarios
     */
    private async loadGroundTruthScenarios(): Promise<void> {
        // Known Sims 4 conflict scenarios based on research
        this.groundTruthScenarios = [
            // TGI Exact Match Conflicts (True Positives)
            {
                id: 'tgi_exact_match_1',
                name: 'Exact TGI Match - Same Resource Different Content',
                description: 'Two mods with identical TGI but different content - classic override conflict',
                resource1: this.createMockResource(0x0166038C, 0x00000000, 0x12345678, 'Mod A Tuning'),
                resource2: this.createMockResource(0x0166038C, 0x00000000, 0x12345678, 'Mod B Tuning'),
                expectedConflict: true,
                expectedType: ConflictType.RESOURCE,
                expectedSeverity: ConflictSeverity.HIGH,
                gameplayImpact: 'FUNCTIONAL',
                source: 'KNOWN_SCENARIO',
                confidence: 1.0
            },

            // Similar Content but Different TGI (False Positive Test)
            {
                id: 'similar_content_different_tgi',
                name: 'Similar Content Different TGI - Not a Conflict',
                description: 'Two mods with similar content but different TGI - should not conflict',
                resource1: this.createMockResource(0x0166038C, 0x00000000, 0x11111111, 'Hair Color Red'),
                resource2: this.createMockResource(0x0166038C, 0x00000000, 0x22222222, 'Hair Color Crimson'),
                expectedConflict: false,
                gameplayImpact: 'NONE',
                source: 'KNOWN_SCENARIO',
                confidence: 0.9
            },

            // Different Resource Types (True Negative)
            {
                id: 'different_types_no_conflict',
                name: 'Different Resource Types - No Conflict',
                description: 'Resources of completely different types should not conflict',
                resource1: this.createMockResource(0x0166038C, 0x00000000, 0x33333333, 'Tuning File'),
                resource2: this.createMockResource(0x00B2D882, 0x00000000, 0x33333333, 'Image File'),
                expectedConflict: false,
                gameplayImpact: 'NONE',
                source: 'KNOWN_SCENARIO',
                confidence: 1.0
            },

            // Trait Conflicts (Known Gameplay Impact)
            {
                id: 'trait_conflict_scenario',
                name: 'Conflicting Trait Modifications',
                description: 'Two mods modifying the same trait in incompatible ways',
                resource1: this.createMockResource(0x0166038C, 0x00000000, 0x44444444, 'Trait Neat Modified'),
                resource2: this.createMockResource(0x0166038C, 0x00000000, 0x44444444, 'Trait Neat Override'),
                expectedConflict: true,
                expectedType: ConflictType.RESOURCE,
                expectedSeverity: ConflictSeverity.MEDIUM,
                gameplayImpact: 'FUNCTIONAL',
                source: 'EXPERT_VALIDATED',
                confidence: 0.85
            },

            // Framework Coordination (False Positive Test)
            {
                id: 'framework_coordination',
                name: 'Framework Coordinated Mods - No Conflict',
                description: 'Mods using same framework that coordinate properly',
                resource1: this.createMockResource(0x0166038C, 0x00000000, 0x55555555, 'Framework Mod A'),
                resource2: this.createMockResource(0x0166038C, 0x00000000, 0x66666666, 'Framework Mod B'),
                expectedConflict: false,
                gameplayImpact: 'NONE',
                source: 'COMMUNITY_REPORTED',
                confidence: 0.7
            }
        ];
    }

    /**
     * Create a mock resource for testing
     */
    private createMockResource(type: number, group: number, instance: number, name: string): ResourceInfo {
        return {
            id: Math.floor(Math.random() * 1000000),
            key: {
                type: type,
                group: BigInt(group),
                instance: BigInt(instance)
            },
            metadata: {
                name: name,
                resourceType: 'TUNING',
                contentSnippet: `Mock content for ${name}`
            },
            type: type,
            group: group,
            instance: instance,
            name: name,
            path: `/mock/path/${name.replace(/\s+/g, '_')}.package`,
            size: 1024,
            hash: `mock_hash_${instance}`
        };
    }

    /**
     * Validate conflict detection accuracy against ground truth scenarios
     */
    public async validateAccuracy(
        conflictDetector: any,
        scenarios?: GroundTruthConflict[]
    ): Promise<AccuracyMetrics> {
        const testScenarios = scenarios || this.groundTruthScenarios;
        this.logger.info(`Validating conflict detection accuracy against ${testScenarios.length} scenarios`);

        const results: ValidationResult[] = [];

        for (const scenario of testScenarios) {
            try {
                const result = await this.validateSingleScenario(conflictDetector, scenario);
                results.push(result);

                this.logger.debug(`Scenario ${scenario.id}: ${result.truePositive ? 'TP' : result.falsePositive ? 'FP' : result.falseNegative ? 'FN' : 'TN'}`);
            } catch (error: any) {
                this.logger.error(`Error validating scenario ${scenario.id}: ${error.message}`);

                // Create a failed result
                results.push({
                    groundTruthId: scenario.id,
                    detected: false,
                    correctType: false,
                    correctSeverity: false,
                    detectedConflicts: [],
                    truePositive: false,
                    falsePositive: false,
                    falseNegative: scenario.expectedConflict,
                    confidence: 0,
                    notes: [`Validation failed: ${error.message}`]
                });
            }
        }

        // Calculate accuracy metrics
        const metrics = this.calculateAccuracyMetrics(results, testScenarios);

        this.logger.info(`Validation complete. Accuracy: ${(metrics.accuracy * 100).toFixed(1)}%, Precision: ${(metrics.precision * 100).toFixed(1)}%, Recall: ${(metrics.recall * 100).toFixed(1)}%`);

        return metrics;
    }

    /**
     * Validate a single conflict scenario
     */
    private async validateSingleScenario(
        conflictDetector: any,
        scenario: GroundTruthConflict
    ): Promise<ValidationResult> {
        // Run conflict detection on the scenario
        const detectedConflicts = await conflictDetector.detectConflicts(
            scenario.resource1,
            scenario.resource2
        );

        const hasDetectedConflict = detectedConflicts && detectedConflicts.length > 0;

        // Determine validation result
        const truePositive = scenario.expectedConflict && hasDetectedConflict;
        const falsePositive = !scenario.expectedConflict && hasDetectedConflict;
        const falseNegative = scenario.expectedConflict && !hasDetectedConflict;

        // Check type and severity accuracy
        let correctType = true;
        let correctSeverity = true;

        if (hasDetectedConflict && scenario.expectedType) {
            correctType = detectedConflicts.some((c: ConflictInfo) => c.type === scenario.expectedType);
        }

        if (hasDetectedConflict && scenario.expectedSeverity) {
            correctSeverity = detectedConflicts.some((c: ConflictInfo) => c.severity === scenario.expectedSeverity);
        }

        const notes: string[] = [];
        if (falsePositive) {
            notes.push('False positive: Detected conflict where none should exist');
        }
        if (falseNegative) {
            notes.push('False negative: Failed to detect expected conflict');
        }
        if (hasDetectedConflict && !correctType) {
            notes.push(`Incorrect conflict type detected. Expected: ${scenario.expectedType}`);
        }
        if (hasDetectedConflict && !correctSeverity) {
            notes.push(`Incorrect severity detected. Expected: ${scenario.expectedSeverity}`);
        }

        // Create a safe version of detected conflicts without circular references
        const safeDetectedConflicts = (detectedConflicts || []).map((conflict: any) => ({
            id: conflict.id || 'unknown',
            type: conflict.type || 'UNKNOWN',
            severity: conflict.severity || 'UNKNOWN',
            description: conflict.description || 'No description',
            confidence: conflict.confidence || 0,
            timestamp: conflict.timestamp || Date.now()
            // Exclude complex objects that might have circular references
        }));

        return {
            groundTruthId: scenario.id,
            detected: hasDetectedConflict,
            correctType,
            correctSeverity,
            detectedConflicts: safeDetectedConflicts,
            truePositive,
            falsePositive,
            falseNegative,
            confidence: scenario.confidence,
            notes
        };
    }

    /**
     * Calculate comprehensive accuracy metrics
     */
    private calculateAccuracyMetrics(
        results: ValidationResult[],
        scenarios: GroundTruthConflict[]
    ): AccuracyMetrics {
        const tp = results.filter(r => r.truePositive).length;
        const fp = results.filter(r => r.falsePositive).length;
        const fn = results.filter(r => r.falseNegative).length;
        const tn = results.length - tp - fp - fn;

        const precision = tp + fp > 0 ? tp / (tp + fp) : 0;
        const recall = tp + fn > 0 ? tp / (tp + fn) : 0;
        const f1Score = precision + recall > 0 ? 2 * (precision * recall) / (precision + recall) : 0;
        const accuracy = results.length > 0 ? (tp + tn) / results.length : 0;

        const typeAccuracy = results.filter(r => r.correctType).length / results.length;
        const severityAccuracy = results.filter(r => r.correctSeverity).length / results.length;
        const averageConfidence = results.reduce((sum, r) => sum + r.confidence, 0) / results.length;
        const detectionRate = results.filter(r => r.detected).length / results.length;
        const falsePositiveRate = fp + tn > 0 ? fp / (fp + tn) : 0;

        return {
            totalScenarios: results.length,
            truePositives: tp,
            falsePositives: fp,
            falseNegatives: fn,
            trueNegatives: tn,
            precision,
            recall,
            f1Score,
            accuracy,
            typeAccuracy,
            severityAccuracy,
            averageConfidence,
            detectionRate,
            falsePositiveRate
        };
    }

    /**
     * Get ground truth scenarios for external use
     */
    public getGroundTruthScenarios(): GroundTruthConflict[] {
        return [...this.groundTruthScenarios];
    }

    /**
     * Add custom ground truth scenarios
     */
    public addGroundTruthScenarios(scenarios: GroundTruthConflict[]): void {
        this.groundTruthScenarios.push(...scenarios);
        this.logger.info(`Added ${scenarios.length} custom ground truth scenarios`);
    }
}
