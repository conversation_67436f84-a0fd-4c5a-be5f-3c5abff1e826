#!/usr/bin/env node

/**
 * Test script for the conflict detection accuracy validation system
 * Tests the new accuracy measurement capabilities
 */

import { DatabaseService } from '../services/databaseService.js';
import { AccuracyMeasurementService } from '../services/validation/AccuracyMeasurementService.js';
import { ConflictDetectionService } from '../services/conflict/conflictDetectionService.js';
import { Logger } from '../utils/logging/logger.js';

async function testAccuracyValidation() {
    console.log('🎯 Testing Conflict Detection Accuracy Validation System');
    console.log('=' .repeat(60));

    try {
        // Initialize database service
        console.log('\n📊 Initializing database service...');
        const databaseService = new DatabaseService(':memory:');
        await databaseService.initialize();
        console.log('✅ Database service initialized');

        // Initialize conflict detection service
        console.log('🔍 Initializing conflict detection service...');
        const conflictDetectionService = ConflictDetectionService.getInstance();

        // Initialize accuracy measurement service
        console.log('📏 Initializing accuracy measurement service...');
        const accuracyService = new AccuracyMeasurementService(
            databaseService,
            conflictDetectionService,
            {
                enableRealTimeValidation: false,
                logDetailedResults: true,
                confidenceThreshold: 0.7
            },
            logger
        );

        await accuracyService.initialize();

        // Test 1: Basic accuracy measurement
        console.log('\n🧪 Test 1: Basic Accuracy Measurement');
        console.log('-'.repeat(40));

        const metrics = await accuracyService.measureAccuracy();

        console.log(`✅ Overall Accuracy: ${(metrics.accuracy * 100).toFixed(1)}%`);
        console.log(`✅ Precision: ${(metrics.precision * 100).toFixed(1)}%`);
        console.log(`✅ Recall: ${(metrics.recall * 100).toFixed(1)}%`);
        console.log(`✅ F1 Score: ${(metrics.f1Score * 100).toFixed(1)}%`);
        console.log(`⚠️  False Positive Rate: ${(metrics.falsePositiveRate * 100).toFixed(1)}%`);

        console.log(`\n📊 Detailed Metrics:`);
        console.log(`- True Positives: ${metrics.truePositives}`);
        console.log(`- False Positives: ${metrics.falsePositives}`);
        console.log(`- False Negatives: ${metrics.falseNegatives}`);
        console.log(`- True Negatives: ${metrics.trueNegatives}`);
        console.log(`- Total Scenarios: ${metrics.totalScenarios}`);

        // Test 2: Get real accuracy values (replacing hardcoded 95%)
        console.log('\n🎯 Test 2: Real Accuracy Values (Replacing Hardcoded 95%)');
        console.log('-'.repeat(50));

        const realAccuracy = accuracyService.getRealAccuracyPercentage();
        const realPrecision = accuracyService.getRealPrecisionPercentage();
        const realRecall = accuracyService.getRealRecallPercentage();
        const falsePositiveRate = accuracyService.getFalsePositiveRate();

        console.log(`🔄 OLD: Hardcoded 95% accuracy`);
        console.log(`✅ NEW: Real measured accuracy: ${realAccuracy.toFixed(1)}%`);
        console.log(`✅ NEW: Real measured precision: ${realPrecision.toFixed(1)}%`);
        console.log(`✅ NEW: Real measured recall: ${realRecall.toFixed(1)}%`);
        console.log(`⚠️  NEW: Real false positive rate: ${(falsePositiveRate * 100).toFixed(1)}%`);

        // Test 3: Accuracy acceptability check
        console.log('\n✅ Test 3: Accuracy Acceptability Check');
        console.log('-'.repeat(40));

        const isAcceptable = accuracyService.isAccuracyAcceptable();
        console.log(`Accuracy acceptable (>= 70%): ${isAcceptable ? '✅ YES' : '❌ NO'}`);

        // Test 4: Get improvement recommendations
        console.log('\n💡 Test 4: Improvement Recommendations');
        console.log('-'.repeat(40));

        const recommendations = accuracyService.getValidationRecommendations();
        if (recommendations.length > 0) {
            recommendations.forEach((rec, index) => {
                console.log(`${index + 1}. ${rec}`);
            });
        } else {
            console.log('✅ No recommendations - accuracy is acceptable');
        }

        // Test 5: Individual detector accuracy (if available)
        console.log('\n🔍 Test 5: Individual Detector Accuracy');
        console.log('-'.repeat(40));

        try {
            const lshAccuracy = await accuracyService.measureDetectorAccuracy('lsh');
            console.log(`LSH Detector:`);
            console.log(`  - Accuracy: ${(lshAccuracy.accuracy * 100).toFixed(1)}%`);
            console.log(`  - Precision: ${(lshAccuracy.precision * 100).toFixed(1)}%`);
            console.log(`  - Recall: ${(lshAccuracy.recall * 100).toFixed(1)}%`);
        } catch (error: any) {
            console.log(`LSH Detector: ⚠️  ${error.message}`);
        }

        try {
            const tgiAccuracy = await accuracyService.measureDetectorAccuracy('tgi');
            console.log(`TGI Detector:`);
            console.log(`  - Accuracy: ${(tgiAccuracy.accuracy * 100).toFixed(1)}%`);
            console.log(`  - Precision: ${(tgiAccuracy.precision * 100).toFixed(1)}%`);
            console.log(`  - Recall: ${(tgiAccuracy.recall * 100).toFixed(1)}%`);
        } catch (error: any) {
            console.log(`TGI Detector: ⚠️  ${error.message}`);
        }

        // Test 6: Ground truth scenarios
        console.log('\n📋 Test 6: Ground Truth Scenarios');
        console.log('-'.repeat(40));

        const groundTruthScenarios = accuracyService.getGroundTruthScenarios();
        console.log(`Total ground truth scenarios: ${groundTruthScenarios.length}`);

        groundTruthScenarios.forEach((scenario, index) => {
            console.log(`${index + 1}. ${scenario.name}`);
            console.log(`   Expected: ${scenario.expectedConflict ? 'CONFLICT' : 'NO CONFLICT'}`);
            console.log(`   Impact: ${scenario.gameplayImpact}`);
            console.log(`   Confidence: ${(scenario.confidence * 100).toFixed(0)}%`);
        });

        console.log('\n🎉 SUCCESS: Accuracy validation system is working!');
        console.log('=' .repeat(60));
        console.log('✅ All tests completed successfully');
        console.log('✅ Real accuracy metrics are now available');
        console.log('✅ Hardcoded 95% values can be replaced');
        console.log('✅ Ground truth validation is operational');
        console.log('✅ Individual detector testing is available');
        console.log('✅ Improvement recommendations are generated');

        // Cleanup
        await databaseService.close();

    } catch (error: any) {
        console.error('\n❌ ERROR during accuracy validation test:', error.message);
        console.error('Stack trace:', error.stack);
        process.exit(1);
    }
}

// Run the test
if (import.meta.url === `file://${process.argv[1]}`) {
    testAccuracyValidation().catch(error => {
        console.error('Fatal error:', error);
        process.exit(1);
    });
}
